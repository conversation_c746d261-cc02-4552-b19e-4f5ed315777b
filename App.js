import React from 'react';
import {
  Safe<PERSON>reaView,
  <PERSON>rollView,
  StatusBar,
  StyleSheet,
  Text,
  View,
  Image,
  Dimensions,
  TouchableOpacity,
} from 'react-native';
import LinearGradient from 'react-native-linear-gradient';
import {
  widthPercentageToDP as wp,
  heightPercentageToDP as hp,
} from 'react-native-responsive-dimensions';

const {width: screenWidth, height: screenHeight} = Dimensions.get('window');

// Responsive utility functions
const isTablet = screenWidth >= 768;
const getResponsiveSize = (size) => isTablet ? size * 1.2 : size;

// Custom Components
const Header = () => (
  <View style={styles.headerContainer}>
    <View style={styles.headerContent}>
      <View style={styles.headerTextContainer}>
        <Text style={styles.headerTitle}>Let's make today count</Text>
        <Text style={styles.headerDate}>June 30th, 2022</Text>
        <Text style={styles.headerWelcome}>Welcome Back!</Text>
      </View>
      <View style={styles.profileImageContainer}>
        <Image
          source={{uri: 'https://via.placeholder.com/60x60/FF6B6B/FFFFFF?text=U'}}
          style={styles.profileImage}
          resizeMode="cover"
        />
      </View>
    </View>
  </View>
);

const UserProfileCard = () => (
  <View style={styles.profileCard}>
    <View style={styles.profileCardContent}>
      <View style={styles.profileInfo}>
        <Text style={styles.profileName}>Cameron Williamson</Text>
        <Text style={styles.profilePhone}>+91 9876543210</Text>
        <Text style={styles.profileAmount}>Rs. 10,000.00</Text>
      </View>
      <View style={styles.profileStatusContainer}>
        <View style={styles.profileStatusDot} />
      </View>
    </View>
  </View>
);

const ContactItem = ({name, email, message, time, avatar, isLast}) => (
  <View style={[styles.contactItem, isLast && styles.contactItemLast]}>
    <View style={styles.contactContent}>
      <Image
        source={{uri: avatar}}
        style={styles.contactAvatar}
        resizeMode="cover"
      />
      <View style={styles.contactInfo}>
        <Text style={styles.contactName}>{name}</Text>
        <Text style={styles.contactEmail}>{email}</Text>
        <Text style={styles.contactMessage}>{message}</Text>
        <View style={styles.contactTimeContainer}>
          <View style={styles.timeIcon} />
          <Text style={styles.contactTime}>{time}</Text>
        </View>
      </View>
      <TouchableOpacity style={styles.contactCheckbox}>
        <View style={styles.checkbox} />
      </TouchableOpacity>
    </View>
  </View>
);

const ExploreSection = () => {
  const contacts = [
    {
      id: 1,
      name: 'Jane Cooper',
      email: '<EMAIL>',
      message: 'Lorem text Lorem text Lorem text Lorem text Lorem text text Lorem text Lorem text Lorem text...',
      time: '28/06/2022 10:00 AM',
      avatar: 'https://via.placeholder.com/50x50/4ECDC4/FFFFFF?text=JC',
    },
    {
      id: 2,
      name: 'Hellena John',
      email: '<EMAIL>',
      message: 'Lorem text Lorem text Lorem text Lorem text Lorem text text Lorem text Lorem text Lorem text...',
      time: '28/06/2022 10:00 AM',
      avatar: 'https://via.placeholder.com/50x50/45B7D1/FFFFFF?text=HJ',
    },
    {
      id: 3,
      name: 'Hellen Jummy',
      email: '<EMAIL>',
      message: 'Lorem text Lorem text Lorem text Lorem text Lorem text text Lorem text Lorem text Lorem text...',
      time: '28/06/2022 10:00 AM',
      avatar: 'https://via.placeholder.com/50x50/96CEB4/FFFFFF?text=HJ',
    },
  ];

  return (
    <View style={styles.exploreSection}>
      <View style={styles.exploreTitleContainer}>
        <View style={styles.exploreAccent} />
        <Text style={styles.exploreTitle}>Explore</Text>
      </View>
      {contacts.map((contact, index) => (
        <ContactItem
          key={contact.id}
          {...contact}
          isLast={index === contacts.length - 1}
        />
      ))}
    </View>
  );
};

const BottomNavigation = () => (
  <LinearGradient
    colors={['#7ED321', '#4CD964']}
    start={{x: 0, y: 0}}
    end={{x: 1, y: 0}}
    style={styles.bottomNav}>
    <TouchableOpacity style={styles.navItem}>
      <View style={styles.navIcon}>
        <View style={[styles.navIconBar, {width: wp('4%')}]} />
        <View style={[styles.navIconBar, {width: wp('3%')}]} />
        <View style={[styles.navIconBar, {width: wp('2%')}]} />
      </View>
    </TouchableOpacity>
    <TouchableOpacity style={styles.navItem}>
      <View style={styles.navIconCheck}>
        <Text style={styles.navIconCheckText}>✓</Text>
      </View>
    </TouchableOpacity>
    <TouchableOpacity style={styles.navItem}>
      <View style={styles.navIconEdit}>
        <Text style={styles.navIconEditText}>✎</Text>
      </View>
    </TouchableOpacity>
    <TouchableOpacity style={styles.navItem}>
      <View style={styles.navIconCircle} />
    </TouchableOpacity>
  </LinearGradient>
);

const App = () => {
  return (
    <SafeAreaView style={styles.container}>
      <StatusBar barStyle="light-content" backgroundColor="#2C4A5C" />
      <ScrollView
        style={styles.scrollView}
        contentContainerStyle={styles.scrollContent}
        showsVerticalScrollIndicator={false}>
        <Header />
        <UserProfileCard />
        <ExploreSection />
      </ScrollView>
      <BottomNavigation />
    </SafeAreaView>
  );
};

const styles = StyleSheet.create({
  container: {
    flex: 1,
    backgroundColor: '#2C4A5C',
  },
  scrollView: {
    flex: 1,
  },
  scrollContent: {
    paddingBottom: hp('2%'),
  },

  // Header Styles
  headerContainer: {
    backgroundColor: '#2C4A5C',
    paddingTop: hp('2%'),
    paddingHorizontal: wp('6%'),
    paddingBottom: hp('3%'),
  },
  headerContent: {
    flexDirection: 'row',
    justifyContent: 'space-between',
    alignItems: 'flex-start',
  },
  headerTextContainer: {
    flex: 1,
    paddingRight: wp('4%'),
  },
  headerTitle: {
    fontSize: wp('7.5%'),
    fontWeight: '700',
    color: '#FFFFFF',
    lineHeight: wp('9%'),
    marginBottom: hp('2%'),
    fontFamily: 'System',
  },
  headerDate: {
    fontSize: wp('4.2%'),
    color: '#FFFFFF',
    opacity: 0.8,
    marginBottom: hp('0.5%'),
    fontWeight: '400',
  },
  headerWelcome: {
    fontSize: wp('4.2%'),
    color: '#FFFFFF',
    opacity: 0.8,
    fontWeight: '400',
  },
  profileImageContainer: {
    width: wp('15%'),
    height: wp('15%'),
    borderRadius: wp('2%'),
    overflow: 'hidden',
    backgroundColor: '#FF6B6B',
  },
  profileImage: {
    width: '100%',
    height: '100%',
  },

  // Profile Card Styles
  profileCard: {
    backgroundColor: '#3A5A6C',
    marginHorizontal: wp('6%'),
    marginTop: hp('1%'),
    borderRadius: wp('4%'),
    padding: wp('5%'),
    marginBottom: hp('3%'),
  },
  profileCardContent: {
    flexDirection: 'row',
    justifyContent: 'space-between',
    alignItems: 'center',
  },
  profileInfo: {
    flex: 1,
  },
  profileName: {
    fontSize: wp('5%'),
    fontWeight: '600',
    color: '#FFFFFF',
    marginBottom: hp('0.8%'),
  },
  profilePhone: {
    fontSize: wp('3.8%'),
    color: '#FFFFFF',
    opacity: 0.8,
    marginBottom: hp('1%'),
  },
  profileAmount: {
    fontSize: wp('4.5%'),
    fontWeight: '600',
    color: '#7ED321',
  },
  profileStatusContainer: {
    alignItems: 'center',
    justifyContent: 'center',
  },
  profileStatusDot: {
    width: wp('3%'),
    height: wp('3%'),
    borderRadius: wp('1.5%'),
    backgroundColor: '#7ED321',
  },

  // Explore Section Styles
  exploreSection: {
    paddingHorizontal: wp('6%'),
  },
  exploreTitleContainer: {
    flexDirection: 'row',
    alignItems: 'center',
    marginBottom: hp('2%'),
  },
  exploreAccent: {
    width: wp('1%'),
    height: hp('3%'),
    backgroundColor: '#7ED321',
    marginRight: wp('3%'),
    borderRadius: wp('0.5%'),
  },
  exploreTitle: {
    fontSize: wp('5.5%'),
    fontWeight: '600',
    color: '#FFFFFF',
  },

  // Contact Item Styles
  contactItem: {
    backgroundColor: '#3A5A6C',
    borderRadius: wp('4%'),
    padding: wp('4%'),
    marginBottom: hp('1.5%'),
  },
  contactItemLast: {
    marginBottom: hp('12%'),
  },
  contactContent: {
    flexDirection: 'row',
    alignItems: 'flex-start',
  },
  contactAvatar: {
    width: wp('12%'),
    height: wp('12%'),
    borderRadius: wp('6%'),
    marginRight: wp('3%'),
  },
  contactInfo: {
    flex: 1,
    paddingRight: wp('3%'),
  },
  contactName: {
    fontSize: wp('4.2%'),
    fontWeight: '600',
    color: '#FFFFFF',
    marginBottom: hp('0.3%'),
  },
  contactEmail: {
    fontSize: wp('3.5%'),
    color: '#FFFFFF',
    opacity: 0.7,
    marginBottom: hp('0.8%'),
  },
  contactMessage: {
    fontSize: wp('3.3%'),
    color: '#FFFFFF',
    opacity: 0.8,
    lineHeight: wp('4.5%'),
    marginBottom: hp('1%'),
  },
  contactTimeContainer: {
    flexDirection: 'row',
    alignItems: 'center',
  },
  timeIcon: {
    width: wp('3%'),
    height: wp('3%'),
    borderRadius: wp('1.5%'),
    backgroundColor: '#FFFFFF',
    opacity: 0.6,
    marginRight: wp('2%'),
  },
  contactTime: {
    fontSize: wp('3%'),
    color: '#FFFFFF',
    opacity: 0.6,
  },
  contactCheckbox: {
    padding: wp('2%'),
  },
  checkbox: {
    width: wp('5%'),
    height: wp('5%'),
    borderWidth: 2,
    borderColor: '#FFFFFF',
    borderRadius: wp('1%'),
    opacity: 0.6,
  },

  // Bottom Navigation Styles
  bottomNav: {
    position: 'absolute',
    bottom: 0,
    left: 0,
    right: 0,
    height: hp('10%'),
    flexDirection: 'row',
    alignItems: 'center',
    justifyContent: 'space-around',
    paddingHorizontal: wp('8%'),
    paddingBottom: hp('2%'),
  },
  navItem: {
    alignItems: 'center',
    justifyContent: 'center',
    padding: wp('3%'),
  },
  navIcon: {
    alignItems: 'flex-start',
  },
  navIconBar: {
    height: wp('0.8%'),
    backgroundColor: '#FFFFFF',
    marginBottom: wp('0.5%'),
    borderRadius: wp('0.4%'),
  },
  navIconCheck: {
    width: wp('6%'),
    height: wp('6%'),
    backgroundColor: '#FFFFFF',
    borderRadius: wp('1%'),
    alignItems: 'center',
    justifyContent: 'center',
  },
  navIconCheckText: {
    color: '#7ED321',
    fontSize: wp('4%'),
    fontWeight: 'bold',
  },
  navIconEdit: {
    width: wp('6%'),
    height: wp('6%'),
    alignItems: 'center',
    justifyContent: 'center',
  },
  navIconEditText: {
    color: '#FFFFFF',
    fontSize: wp('4.5%'),
    fontWeight: 'bold',
  },
  navIconCircle: {
    width: wp('6%'),
    height: wp('6%'),
    borderRadius: wp('3%'),
    backgroundColor: '#FFFFFF',
    opacity: 0.8,
  },
});

export default App;
