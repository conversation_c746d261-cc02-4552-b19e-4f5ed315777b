<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>About - React Native & Full Stack Developer</title>
    <link href="https://cdnjs.cloudflare.com/ajax/libs/font-awesome/6.0.0/css/all.min.css" rel="stylesheet">
    <style>
        * {
            margin: 0;
            padding: 0;
            box-sizing: border-box;
        }
        
        body {
            font-family: 'Segoe UI', Tahoma, Geneva, Verdana, sans-serif;
            line-height: 1.6;
            color: #333;
            background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
            min-height: 100vh;
        }
        
        .container {
            max-width: 1200px;
            margin: 0 auto;
            padding: 2rem;
        }
        
        .about-section {
            background: rgba(255, 255, 255, 0.95);
            border-radius: 20px;
            padding: 3rem;
            box-shadow: 0 20px 40px rgba(0, 0, 0, 0.1);
            backdrop-filter: blur(10px);
        }
        
        .header {
            text-align: center;
            margin-bottom: 3rem;
        }
        
        .header h1 {
            font-size: 3rem;
            color: #2c3e50;
            margin-bottom: 1rem;
            background: linear-gradient(45deg, #667eea, #764ba2);
            -webkit-background-clip: text;
            -webkit-text-fill-color: transparent;
        }
        
        .header .subtitle {
            font-size: 1.3rem;
            color: #7f8c8d;
            font-weight: 300;
        }
        
        .intro {
            text-align: center;
            font-size: 1.2rem;
            color: #34495e;
            margin-bottom: 3rem;
            line-height: 1.8;
        }
        
        .skills-grid {
            display: grid;
            grid-template-columns: repeat(auto-fit, minmax(300px, 1fr));
            gap: 2rem;
            margin-bottom: 3rem;
        }
        
        .skill-category {
            background: #f8f9fa;
            padding: 2rem;
            border-radius: 15px;
            border-left: 5px solid #667eea;
        }
        
        .skill-category h3 {
            color: #2c3e50;
            margin-bottom: 1rem;
            font-size: 1.3rem;
        }
        
        .skill-category ul {
            list-style: none;
        }
        
        .skill-category li {
            padding: 0.5rem 0;
            color: #555;
            position: relative;
            padding-left: 1.5rem;
        }
        
        .skill-category li:before {
            content: "✓";
            position: absolute;
            left: 0;
            color: #27ae60;
            font-weight: bold;
        }
        
        .experience-section {
            background: #2c3e50;
            color: white;
            padding: 2rem;
            border-radius: 15px;
            margin-bottom: 2rem;
        }
        
        .experience-section h2 {
            color: #ecf0f1;
            margin-bottom: 1.5rem;
            font-size: 2rem;
        }
        
        .experience-item {
            margin-bottom: 1.5rem;
            padding-left: 1rem;
            border-left: 3px solid #3498db;
        }
        
        .cta-section {
            text-align: center;
            background: linear-gradient(45deg, #667eea, #764ba2);
            color: white;
            padding: 2rem;
            border-radius: 15px;
        }
        
        .cta-section h2 {
            margin-bottom: 1rem;
        }
        
        .cta-section p {
            font-size: 1.1rem;
            margin-bottom: 1.5rem;
        }
        
        .contact-btn {
            display: inline-block;
            background: white;
            color: #667eea;
            padding: 1rem 2rem;
            border-radius: 50px;
            text-decoration: none;
            font-weight: bold;
            transition: transform 0.3s ease;
        }
        
        .contact-btn:hover {
            transform: translateY(-3px);
            box-shadow: 0 10px 20px rgba(0, 0, 0, 0.2);
        }
        
        @media (max-width: 768px) {
            .container {
                padding: 1rem;
            }
            
            .about-section {
                padding: 2rem;
            }
            
            .header h1 {
                font-size: 2rem;
            }
            
            .skills-grid {
                grid-template-columns: 1fr;
            }
        }
    </style>
</head>
<body>
    <div class="container">
        <div class="about-section">
            <div class="header">
                <h1>React Native & Full Stack Developer</h1>
                <p class="subtitle">5+ Years of Professional Experience</p>
            </div>
            
            <div class="intro">
                <p>Hello! I have the skills to develop an online presence for your business. Do you need a simple website? A dynamic interactive website? A mobile application? Yes! Then I am the man for your job.</p>
            </div>
            
            <div class="skills-grid">
                <div class="skill-category">
                    <h3><i class="fas fa-mobile-alt"></i> Frontend & Mobile</h3>
                    <ul>
                        <li>React.js - Advanced</li>
                        <li>Next.js - Expert Level</li>
                        <li>React Native - 5+ Years</li>
                        <li>Flutter - Professional</li>
                        <li>Pixel Perfect Design</li>
                        <li>Responsive UI/UX</li>
                    </ul>
                </div>
                
                <div class="skill-category">
                    <h3><i class="fas fa-server"></i> Backend & APIs</h3>
                    <ul>
                        <li>Node.js - 5+ Years</li>
                        <li>RESTful APIs</li>
                        <li>Third-party Integrations</li>
                        <li>Database Management</li>
                        <li>API Implementation</li>
                        <li>Performance Optimization</li>
                    </ul>
                </div>
                
                <div class="skill-category">
                    <h3><i class="fas fa-brain"></i> AI & Modern Tech</h3>
                    <ul>
                        <li>OpenAI Integration</li>
                        <li>Large Language Models (LLMs)</li>
                        <li>AI-powered Applications</li>
                        <li>Redux Architecture</li>
                        <li>State Management</li>
                        <li>Modern Frameworks</li>
                    </ul>
                </div>
            </div>
            
            <div class="experience-section">
                <h2><i class="fas fa-briefcase"></i> Professional Experience</h2>
                
                <div class="experience-item">
                    <h4>Mobile Application Development</h4>
                    <p>• Design and implement user interface components for JavaScript-based mobile applications using React Native</p>
                    <p>• Build app and UI components from prototypes and wireframes with pixel-perfect precision</p>
                    <p>• Use Native APIs for tight integrations with both Android and iOS platforms</p>
                </div>
                
                <div class="experience-item">
                    <h4>Team Collaboration & Quality Assurance</h4>
                    <p>• Work as part of small teams with React Native developers, designers, QA experts, and managers</p>
                    <p>• Write automated tests to ensure error-free code and optimal performance</p>
                    <p>• Coordinate with cross-functional teams to deliver apps within stipulated time and budget</p>
                </div>
                
                <div class="experience-item">
                    <h4>Performance & Architecture</h4>
                    <p>• Improve front-end performance by eliminating performance bottlenecks</p>
                    <p>• Create front-end modules with maximum code reusability and efficiency</p>
                    <p>• Work with Redux architecture to improve performance of websites and mobile apps</p>
                    <p>• Implement clean, smooth animations for excellent user interfaces</p>
                </div>
            </div>
            
            <div class="cta-section">
                <h2>Ready to Build Your Next Project?</h2>
                <p>With 5+ years of experience in React Native and Node.js, I deliver pixel-perfect, high-performance solutions that bring your vision to life.</p>
                <a href="#contact" class="contact-btn">Let's Work Together</a>
            </div>
        </div>
    </div>
</body>
</html>
