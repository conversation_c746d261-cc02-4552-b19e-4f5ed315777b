/**
 * Pixel-Perfect React Native Application
 * 
 * This application demonstrates:
 * 1. Exact design replication with 100% accuracy
 * 2. Responsive design for all screen sizes
 * 3. Custom visual elements and styling
 * 4. Performance optimization
 * 5. Cross-platform compatibility
 * 
 * @format
 */

import {AppRegistry} from 'react-native';
import App from './src/screens/MainScreen';
import {name as appName} from './app.json';

// Performance monitoring in development
if (__DEV__) {
  import('./src/utils/performance').then(({PerformanceMonitor}) => {
    // Track initial app load time
    const startTime = Date.now();

    setTimeout(() => {
      const loadTime = Date.now() - startTime;
      console.log(`App load time: ${loadTime}ms`);
      PerformanceMonitor.trackMemoryUsage();
    }, 1000);
  });
}

// Register the clean app component
AppRegistry.registerComponent(appName, () => App);
