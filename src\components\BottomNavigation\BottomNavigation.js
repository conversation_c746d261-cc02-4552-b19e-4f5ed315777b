import React from 'react';
import {View, Text, TouchableOpacity} from 'react-native';
import LinearGradient from 'react-native-linear-gradient';
import {COLORS} from '../../utils/responsive';
import {styles} from './BottomNavigation.styles';
import {
  widthPercentageToDP as wp,
} from 'react-native-responsive-dimensions';

const BottomNavigation = () => {
  return (
    <LinearGradient
      colors={COLORS.GRADIENT_GREEN}
      start={{x: 0, y: 0}}
      end={{x: 1, y: 0}}
      style={styles.bottomNav}>
      <TouchableOpacity style={styles.navItem} activeOpacity={0.7}>
        <View style={styles.navIcon}>
          <View style={[styles.navIconBar, {width: wp('4%')}]} />
          <View style={[styles.navIconBar, {width: wp('3%')}]} />
          <View style={[styles.navIconBar, {width: wp('2%')}]} />
        </View>
      </TouchableOpacity>
      <TouchableOpacity style={styles.navItem} activeOpacity={0.7}>
        <View style={styles.navIconCheck}>
          <Text style={styles.navIconCheckText}>✓</Text>
        </View>
      </TouchableOpacity>
      <TouchableOpacity style={styles.navItem} activeOpacity={0.7}>
        <View style={styles.navIconEdit}>
          <Text style={styles.navIconEditText}>✎</Text>
        </View>
      </TouchableOpacity>
      <TouchableOpacity style={styles.navItem} activeOpacity={0.7}>
        <View style={styles.navIconCircle} />
      </TouchableOpacity>
    </LinearGradient>
  );
};

export default BottomNavigation;
