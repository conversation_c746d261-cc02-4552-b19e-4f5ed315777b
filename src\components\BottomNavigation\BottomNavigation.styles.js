import {StyleSheet} from 'react-native';
import {COLORS, LAYOUT, getFontSize, getSpacing, normalize} from '../../utils/responsive';
import {
  widthPercentageToDP as wp,
  heightPercentageToDP as hp,
} from 'react-native-responsive-dimensions';

export const styles = StyleSheet.create({
  bottomNav: {
    position: 'absolute',
    bottom: 0,
    left: 0,
    right: 0,
    height: LAYOUT.BOTTOM_NAV_HEIGHT,
    flexDirection: 'row',
    alignItems: 'center',
    justifyContent: 'space-around',
    paddingHorizontal: getSpacing(wp('8%')),
    paddingBottom: getSpacing(hp('2%')),
    elevation: 10,
    shadowColor: '#000',
    shadowOffset: {width: 0, height: -2},
    shadowOpacity: 0.25,
    shadowRadius: 3.84,
  },
  navItem: {
    alignItems: 'center',
    justifyContent: 'center',
    padding: getSpacing(wp('3%')),
  },
  navIcon: {
    alignItems: 'flex-start',
  },
  navIconBar: {
    height: normalize(3),
    backgroundColor: COLORS.WHITE,
    marginBottom: normalize(2),
    borderRadius: normalize(1.5),
  },
  navIconCheck: {
    width: normalize(24),
    height: normalize(24),
    backgroundColor: COLORS.WHITE,
    borderRadius: normalize(4),
    alignItems: 'center',
    justifyContent: 'center',
  },
  navIconCheckText: {
    color: COLORS.ACCENT_GREEN,
    fontSize: getFontSize(14),
    fontWeight: 'bold',
  },
  navIconEdit: {
    width: normalize(24),
    height: normalize(24),
    alignItems: 'center',
    justifyContent: 'center',
  },
  navIconEditText: {
    color: COLORS.WHITE,
    fontSize: getFontSize(16),
    fontWeight: 'bold',
  },
  navIconCircle: {
    width: normalize(24),
    height: normalize(24),
    borderRadius: normalize(12),
    backgroundColor: COLORS.WHITE,
    opacity: 0.8,
  },
});
