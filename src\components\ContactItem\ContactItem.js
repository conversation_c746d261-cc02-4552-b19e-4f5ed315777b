import React from 'react';
import {View, Text, TouchableOpacity} from 'react-native';
import FastImage from 'react-native-fast-image';
import {styles} from './ContactItem.styles';

const ContactItem = ({name, email, message, time, avatar, isLast}) => {
  return (
    <View style={[styles.contactItem, isLast && styles.contactItemLast]}>
      <View style={styles.contactContent}>
        <FastImage
          source={{
            uri: avatar,
            priority: FastImage.priority.normal,
          }}
          style={styles.contactAvatar}
          resizeMode={FastImage.resizeMode.cover}
        />
        <View style={styles.contactInfo}>
          <Text style={styles.contactName} numberOfLines={1}>
            {name}
          </Text>
          <Text style={styles.contactEmail} numberOfLines={1}>
            {email}
          </Text>
          <Text style={styles.contactMessage} numberOfLines={2}>
            {message}
          </Text>
          <View style={styles.contactTimeContainer}>
            <View style={styles.timeIcon} />
            <Text style={styles.contactTime}>{time}</Text>
          </View>
        </View>
        <TouchableOpacity
          style={styles.contactCheckbox}
          activeOpacity={0.7}
          hitSlop={{top: 10, bottom: 10, left: 10, right: 10}}>
          <View style={styles.checkbox} />
        </TouchableOpacity>
      </View>
    </View>
  );
};

export default ContactItem;
