import {StyleSheet} from 'react-native';
import {COLORS, TYPOGRAPHY, LAYOUT, getSpacing, normalize, wp, hp} from '../../utils/responsive';

export const styles = StyleSheet.create({
  contactItem: {
    backgroundColor: COLORS.SECONDARY_DARK,
    borderRadius: LAYOUT.CARD_BORDER_RADIUS,
    padding: getSpacing(wp('4%')),
    marginBottom: getSpacing(hp('1.5%')),
    elevation: 3,
    shadowColor: '#000',
    shadowOffset: {width: 0, height: 1},
    shadowOpacity: 0.22,
    shadowRadius: 2.22,
  },
  contactItemLast: {
    marginBottom: getSpacing(hp('2%')),
  },
  contactContent: {
    flexDirection: 'row',
    alignItems: 'flex-start',
  },
  contactAvatar: {
    width: LAYOUT.AVATAR_SIZE,
    height: LAYOUT.AVATAR_SIZE,
    borderRadius: LAYOUT.AVATAR_SIZE / 2,
    marginRight: getSpacing(wp('3%')),
  },
  contactInfo: {
    flex: 1,
    paddingRight: getSpacing(wp('3%')),
  },
  contactName: {
    ...TYPOGRAPHY.CONTACT_NAME,
    color: COLORS.TEXT_PRIMARY,
    marginBottom: getSpacing(hp('0.3%')),
  },
  contactEmail: {
    ...TYPOGRAPHY.CONTACT_EMAIL,
    color: COLORS.TEXT_SECONDARY,
    marginBottom: getSpacing(hp('0.8%')),
  },
  contactMessage: {
    ...TYPOGRAPHY.CONTACT_MESSAGE,
    color: COLORS.TEXT_SECONDARY,
    marginBottom: getSpacing(hp('1%')),
  },
  contactTimeContainer: {
    flexDirection: 'row',
    alignItems: 'center',
  },
  timeIcon: {
    width: normalize(10),
    height: normalize(10),
    borderRadius: normalize(5),
    backgroundColor: COLORS.TEXT_TERTIARY,
    marginRight: getSpacing(wp('2%')),
  },
  contactTime: {
    ...TYPOGRAPHY.CONTACT_TIME,
    color: COLORS.TEXT_TERTIARY,
  },
  contactCheckbox: {
    padding: getSpacing(wp('2%')),
  },
  checkbox: {
    width: normalize(18),
    height: normalize(18),
    borderWidth: 2,
    borderColor: COLORS.TEXT_SECONDARY,
    borderRadius: normalize(4),
  },
});
