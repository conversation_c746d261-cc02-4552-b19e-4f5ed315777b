import React from 'react';
import {View, Text} from 'react-native';
import ContactItem from '../ContactItem';
import {styles} from './ExploreSection.styles';

const ExploreSection = () => {
  const contacts = [
    {
      id: 1,
      name: '<PERSON>',
      email: '<EMAIL>',
      message: 'Lorem text Lorem text Lorem text Lorem text Lorem text text Lorem text Lorem text Lorem text...',
      time: '28/06/2022 10:00 AM',
      avatar: 'https://via.placeholder.com/50x50/4ECDC4/FFFFFF?text=JC',
    },
    {
      id: 2,
      name: '<PERSON><PERSON>',
      email: '<EMAIL>',
      message: 'Lorem text Lorem text Lorem text Lorem text Lorem text text Lorem text Lorem text Lorem text...',
      time: '28/06/2022 10:00 AM',
      avatar: 'https://via.placeholder.com/50x50/45B7D1/FFFFFF?text=HJ',
    },
    {
      id: 3,
      name: '<PERSON><PERSON>',
      email: '<EMAIL>',
      message: 'Lorem text Lorem text Lorem text Lorem text Lorem text text Lorem text Lorem text Lorem text...',
      time: '28/06/2022 10:00 AM',
      avatar: 'https://via.placeholder.com/50x50/96CEB4/FFFFFF?text=HJ',
    },
  ];

  return (
    <View style={styles.exploreSection}>
      <View style={styles.exploreTitleContainer}>
        <View style={styles.exploreAccent} />
        <Text style={styles.exploreTitle}>Explore</Text>
      </View>
      {contacts.map((contact, index) => (
        <ContactItem
          key={contact.id}
          {...contact}
          isLast={index === contacts.length - 1}
        />
      ))}
    </View>
  );
};

export default ExploreSection;
