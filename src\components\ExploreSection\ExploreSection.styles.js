import {StyleSheet} from 'react-native';
import {COLORS, TYPOGRAPHY, getSpacing, normalize, wp, hp} from '../../utils/responsive';

export const styles = StyleSheet.create({
  exploreSection: {
    paddingHorizontal: getSpacing(wp('6%')),
  },
  exploreTitleContainer: {
    flexDirection: 'row',
    alignItems: 'center',
    marginBottom: getSpacing(hp('2%')),
  },
  exploreAccent: {
    width: normalize(4),
    height: getSpacing(hp('3%')),
    backgroundColor: COLORS.ACCENT_GREEN,
    marginRight: getSpacing(wp('3%')),
    borderRadius: normalize(2),
  },
  exploreTitle: {
    ...TYPOGRAPHY.SECTION_TITLE,
    color: COLORS.TEXT_PRIMARY,
  },
});
