import React from 'react';
import {View, Text} from 'react-native';
import FastImage from 'react-native-fast-image';
import {styles} from './Header.styles';

const Header = () => {
  return (
    <View style={styles.headerContainer}>
      <View style={styles.headerContent}>
        <View style={styles.headerTextContainer}>
          <Text style={styles.headerTitle}>Let's make today count</Text>
          <Text style={styles.headerDate}>June 30th, 2022</Text>
          <Text style={styles.headerWelcome}>Welcome Back!</Text>
        </View>
        <View style={styles.profileImageContainer}>
          <FastImage
            source={{
              uri: 'https://via.placeholder.com/60x60/FF6B6B/FFFFFF?text=U',
              priority: FastImage.priority.high,
            }}
            style={styles.profileImage}
            resizeMode={FastImage.resizeMode.cover}
          />
        </View>
      </View>
    </View>
  );
};

export default Header;
