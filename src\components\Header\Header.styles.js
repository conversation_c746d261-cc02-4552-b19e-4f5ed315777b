import {StyleSheet} from 'react-native';
import {COLORS, TYPOGRAPHY, getSpacing, wp, hp} from '../../utils/responsive';

export const styles = StyleSheet.create({
  headerContainer: {
    backgroundColor: COLORS.PRIMARY_DARK,
    paddingTop: getSpacing(hp('2%')),
    paddingHorizontal: getSpacing(wp('6%')),
    paddingBottom: getSpacing(hp('3%')),
  },
  headerContent: {
    flexDirection: 'row',
    justifyContent: 'space-between',
    alignItems: 'flex-start',
  },
  headerTextContainer: {
    flex: 1,
    paddingRight: getSpacing(wp('4%')),
  },
  headerTitle: {
    ...TYPOGRAPHY.HEADER_TITLE,
    color: COLORS.TEXT_PRIMARY,
    marginBottom: getSpacing(hp('2%')),
    fontFamily: 'System',
  },
  headerDate: {
    ...TYPOGRAPHY.HEADER_SUBTITLE,
    color: COLORS.TEXT_SECONDARY,
    marginBottom: getSpacing(hp('0.5%')),
  },
  headerWelcome: {
    ...TYPOGRAPHY.HEADER_SUBTITLE,
    color: COLORS.TEXT_SECONDARY,
  },
  profileImageContainer: {
    width: wp('15%'),
    height: wp('15%'),
    borderRadius: wp('2%'),
    overflow: 'hidden',
    backgroundColor: COLORS.PROFILE_ORANGE,
    elevation: 3,
    shadowColor: '#000',
    shadowOffset: {width: 0, height: 2},
    shadowOpacity: 0.25,
    shadowRadius: 3.84,
  },
  profileImage: {
    width: '100%',
    height: '100%',
  },
});
