import React, {useState} from 'react';
import {View, Text, Switch} from 'react-native';
import {COLORS} from '../../utils/responsive';
import {styles} from './ProfileCard.styles';

const ProfileCard = () => {
  const [isProfileActive, setIsProfileActive] = useState(true);

  return (
    <View style={styles.profileCard}>
      <View style={styles.profileCardContent}>
        <View style={styles.profileInfo}>
          <Text style={styles.profileName}><PERSON> Williamson</Text>
          <Text style={styles.profilePhone}>+91 9876543210</Text>
          <Text style={styles.profileAmount}>Rs. 10,000.00</Text>
        </View>
        <View style={styles.profileStatusContainer}>
          <Switch
            trackColor={{
              false: COLORS.TEXT_TERTIARY,
              true: COLORS.ACCENT_GREEN,
            }}
            thumbColor={isProfileActive ? COLORS.WHITE : COLORS.TEXT_SECONDARY}
            ios_backgroundColor={COLORS.TEXT_TERTIARY}
            onValueChange={setIsProfileActive}
            value={isProfileActive}
            style={styles.profileSwitch}
          />
        </View>
      </View>
    </View>
  );
};

export default ProfileCard;
