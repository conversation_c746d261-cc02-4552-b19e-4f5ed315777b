import {StyleSheet} from 'react-native';
import {COLORS, TYPOGRAPHY, LAYOUT, getSpacing, normalize} from '../../utils/responsive';
import {
  widthPercentageToDP as wp,
  heightPercentageToDP as hp,
} from 'react-native-responsive-dimensions';

export const styles = StyleSheet.create({
  profileCard: {
    backgroundColor: COLORS.SECONDARY_DARK,
    marginHorizontal: getSpacing(wp('6%')),
    marginTop: getSpacing(hp('1%')),
    borderRadius: LAYOUT.CARD_BORDER_RADIUS,
    padding: getSpacing(wp('5%')),
    marginBottom: getSpacing(hp('3%')),
    elevation: 5,
    shadowColor: '#000',
    shadowOffset: {width: 0, height: 2},
    shadowOpacity: 0.25,
    shadowRadius: 3.84,
  },
  profileCardContent: {
    flexDirection: 'row',
    justifyContent: 'space-between',
    alignItems: 'center',
  },
  profileInfo: {
    flex: 1,
  },
  profileName: {
    ...TYPOGRAPHY.PROFILE_NAME,
    color: COLORS.TEXT_PRIMARY,
    marginBottom: getSpacing(hp('0.8%')),
  },
  profilePhone: {
    ...TYPOGRAPHY.PROFILE_PHONE,
    color: COLORS.TEXT_SECONDARY,
    marginBottom: getSpacing(hp('1%')),
  },
  profileAmount: {
    ...TYPOGRAPHY.PROFILE_AMOUNT,
    color: COLORS.ACCENT_GREEN,
  },
  profileStatusContainer: {
    alignItems: 'center',
    justifyContent: 'center',
  },
  profileSwitch: {
    transform: [{scaleX: 0.8}, {scaleY: 0.8}],
  },
});
