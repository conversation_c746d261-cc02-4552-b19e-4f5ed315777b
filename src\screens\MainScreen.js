import React from 'react';
import {SafeAreaView, Sc<PERSON>View, StatusBar, StyleSheet} from 'react-native';
import {
  Header,
  ProfileCard,
  ExploreSection,
  BottomNavigation,
} from '../components';
import {COLORS, LAYOUT} from '../utils/responsive';

const MainScreen = () => {
  return (
    <SafeAreaView style={styles.container}>
      <StatusBar barStyle="light-content" backgroundColor={COLORS.PRIMARY_DARK} />
      <ScrollView
        style={styles.scrollView}
        contentContainerStyle={styles.scrollContent}
        showsVerticalScrollIndicator={false}
        bounces={false}
        overScrollMode="never">
        <Header />
        <ProfileCard />
        <ExploreSection />
      </ScrollView>
      <BottomNavigation />
    </SafeAreaView>
  );
};

const styles = StyleSheet.create({
  container: {
    flex: 1,
    backgroundColor: COLORS.PRIMARY_DARK,
  },
  scrollView: {
    flex: 1,
  },
  scrollContent: {
    paddingBottom: LAYOUT.BOTTOM_NAV_HEIGHT,
  },
});

export default MainScreen;
