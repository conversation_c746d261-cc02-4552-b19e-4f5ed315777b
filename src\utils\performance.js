import {InteractionManager, LayoutAnimation, Platform} from 'react-native';

// Performance optimization utilities
export class PerformanceOptimizer {
  static enableLayoutAnimations() {
    if (Platform.OS === 'android') {
      LayoutAnimation.configureNext({
        duration: 300,
        create: {
          type: LayoutAnimation.Types.easeInEaseOut,
          property: LayoutAnimation.Properties.opacity,
        },
        update: {
          type: LayoutAnimation.Types.easeInEaseOut,
        },
      });
    }
  }

  static runAfterInteractions(callback) {
    InteractionManager.runAfterInteractions(() => {
      callback();
    });
  }

  static debounce(func, wait) {
    let timeout;
    return function executedFunction(...args) {
      const later = () => {
        clearTimeout(timeout);
        func(...args);
      };
      clearTimeout(timeout);
      timeout = setTimeout(later, wait);
    };
  }

  static throttle(func, limit) {
    let inThrottle;
    return function() {
      const args = arguments;
      const context = this;
      if (!inThrottle) {
        func.apply(context, args);
        inThrottle = true;
        setTimeout(() => inThrottle = false, limit);
      }
    };
  }

  static memoize(fn) {
    const cache = new Map();
    return (...args) => {
      const key = JSON.stringify(args);
      if (cache.has(key)) {
        return cache.get(key);
      }
      const result = fn(...args);
      cache.set(key, result);
      return result;
    };
  }
}

// Image optimization utilities
export const ImageOptimizer = {
  getOptimizedImageProps: (uri, size = 'medium') => {
    const sizeMap = {
      small: {width: 50, height: 50},
      medium: {width: 100, height: 100},
      large: {width: 200, height: 200},
    };

    const dimensions = sizeMap[size] || sizeMap.medium;
    
    return {
      source: {
        uri,
        priority: 'normal',
        cache: 'immutable',
      },
      style: dimensions,
      resizeMode: 'cover',
      // Enable hardware acceleration
      renderToHardwareTextureAndroid: true,
      shouldRasterizeIOS: true,
    };
  },

  preloadImages: (imageUris) => {
    // Preload images for better performance
    imageUris.forEach(uri => {
      if (Platform.OS === 'ios') {
        // iOS preloading
        const image = new Image();
        image.src = uri;
      }
    });
  },
};

// Memory management utilities
export const MemoryManager = {
  cleanupTimers: (timers) => {
    timers.forEach(timer => {
      if (timer) {
        clearTimeout(timer);
        clearInterval(timer);
      }
    });
  },

  cleanupListeners: (listeners) => {
    listeners.forEach(listener => {
      if (listener && typeof listener.remove === 'function') {
        listener.remove();
      }
    });
  },

  optimizeScrollView: {
    removeClippedSubviews: true,
    maxToRenderPerBatch: 10,
    updateCellsBatchingPeriod: 50,
    initialNumToRender: 10,
    windowSize: 10,
    getItemLayout: (data, index) => ({
      length: 80, // Estimated item height
      offset: 80 * index,
      index,
    }),
  },
};

// Animation performance utilities
export const AnimationOptimizer = {
  getOptimizedAnimationConfig: () => ({
    useNativeDriver: true,
    duration: 300,
    easing: 'ease-in-out',
  }),

  createSpringAnimation: (value, toValue) => ({
    toValue,
    useNativeDriver: true,
    speed: 12,
    bounciness: 8,
  }),

  createTimingAnimation: (value, toValue, duration = 300) => ({
    toValue,
    duration,
    useNativeDriver: true,
    easing: 'ease-in-out',
  }),
};

// Bundle size optimization
export const BundleOptimizer = {
  // Lazy load components
  lazyLoad: (importFunc) => {
    return React.lazy(importFunc);
  },

  // Code splitting utility
  splitComponent: (componentPath) => {
    return React.lazy(() => import(componentPath));
  },
};

// Network optimization
export const NetworkOptimizer = {
  createOptimizedFetch: (url, options = {}) => {
    const defaultOptions = {
      timeout: 10000,
      cache: 'default',
      headers: {
        'Content-Type': 'application/json',
        ...options.headers,
      },
    };

    return fetch(url, {...defaultOptions, ...options});
  },

  batchRequests: (requests, batchSize = 3) => {
    const batches = [];
    for (let i = 0; i < requests.length; i += batchSize) {
      batches.push(requests.slice(i, i + batchSize));
    }
    
    return batches.reduce(async (previousBatch, currentBatch) => {
      await previousBatch;
      return Promise.all(currentBatch.map(request => request()));
    }, Promise.resolve());
  },
};

// Performance monitoring
export const PerformanceMonitor = {
  measureRenderTime: (componentName, renderFunction) => {
    const startTime = Date.now();
    const result = renderFunction();
    const endTime = Date.now();
    
    if (__DEV__) {
      console.log(`${componentName} render time: ${endTime - startTime}ms`);
    }
    
    return result;
  },

  trackMemoryUsage: () => {
    if (__DEV__ && global.performance && global.performance.memory) {
      const memory = global.performance.memory;
      console.log('Memory Usage:', {
        used: Math.round(memory.usedJSHeapSize / 1048576) + ' MB',
        total: Math.round(memory.totalJSHeapSize / 1048576) + ' MB',
        limit: Math.round(memory.jsHeapSizeLimit / 1048576) + ' MB',
      });
    }
  },
};

export default {
  PerformanceOptimizer,
  ImageOptimizer,
  MemoryManager,
  AnimationOptimizer,
  BundleOptimizer,
  NetworkOptimizer,
  PerformanceMonitor,
};
