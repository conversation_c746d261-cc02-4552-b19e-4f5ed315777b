import { Dimensions, PixelRatio } from 'react-native';
import {
  widthPercentageToDP as wp,
  heightPercentageToDP as hp,
} from 'react-native-responsive-dimensions';

const { width: SCREEN_WIDTH, height: SCREEN_HEIGHT } = Dimensions.get('window');

// Device type detection
export const isTablet = SCREEN_WIDTH >= 768;
export const isSmallPhone = SCREEN_WIDTH < 375;
export const isLargePhone = SCREEN_WIDTH >= 414;

// Responsive font scaling
export const getFontSize = (size) => {
  const scale = SCREEN_WIDTH / 375; // Base width for iPhone 6/7/8
  const newSize = size * scale;

  if (isTablet) {
    return Math.max(newSize * 1.1, size);
  }

  if (isSmallPhone) {
    return Math.max(newSize * 0.9, size * 0.85);
  }

  return Math.max(newSize, size);
};

// Responsive spacing
export const getSpacing = (space) => {
  if (isTablet) {
    return space * 1.3;
  }

  if (isSmallPhone) {
    return space * 0.8;
  }

  return space;
};

// Responsive dimensions
export const responsiveWidth = (percentage) => wp(percentage);
export const responsiveHeight = (percentage) => hp(percentage);

// Pixel perfect scaling
export const normalize = (size) => {
  const newSize = size * (SCREEN_WIDTH / 375);
  return Math.round(PixelRatio.roundToNearestPixel(newSize));
};

// Layout constants
export const LAYOUT = {
  HEADER_HEIGHT: isTablet ? hp('12%') : hp('15%'),
  BOTTOM_NAV_HEIGHT: isTablet ? hp('8%') : hp('10%'),
  CARD_BORDER_RADIUS: isTablet ? wp('3%') : wp('4%'),
  AVATAR_SIZE: isTablet ? wp('10%') : wp('12%'),
  PROFILE_IMAGE_SIZE: isTablet ? wp('12%') : wp('15%'),
};

// Color constants
export const COLORS = {
  PRIMARY_DARK: '#2C4A5C',
  SECONDARY_DARK: '#3A5A6C',
  ACCENT_GREEN: '#7ED321',
  GRADIENT_GREEN: ['#7ED321', '#4CD964'],
  WHITE: '#FFFFFF',
  TEXT_PRIMARY: '#FFFFFF',
  TEXT_SECONDARY: 'rgba(255, 255, 255, 0.8)',
  TEXT_TERTIARY: 'rgba(255, 255, 255, 0.6)',
  PROFILE_ORANGE: '#FF6B6B',
};

// Typography
export const TYPOGRAPHY = {
  HEADER_TITLE: {
    fontSize: getFontSize(28),
    fontWeight: '700',
    lineHeight: getFontSize(34),
  },
  HEADER_SUBTITLE: {
    fontSize: getFontSize(16),
    fontWeight: '400',
  },
  PROFILE_NAME: {
    fontSize: getFontSize(18),
    fontWeight: '600',
  },
  PROFILE_PHONE: {
    fontSize: getFontSize(14),
    fontWeight: '400',
  },
  PROFILE_AMOUNT: {
    fontSize: getFontSize(16),
    fontWeight: '600',
  },
  SECTION_TITLE: {
    fontSize: getFontSize(20),
    fontWeight: '600',
  },
  CONTACT_NAME: {
    fontSize: getFontSize(15),
    fontWeight: '600',
  },
  CONTACT_EMAIL: {
    fontSize: getFontSize(13),
    fontWeight: '400',
  },
  CONTACT_MESSAGE: {
    fontSize: getFontSize(12),
    fontWeight: '400',
    lineHeight: getFontSize(16),
  },
  CONTACT_TIME: {
    fontSize: getFontSize(11),
    fontWeight: '400',
  },
};

// Animation constants
export const ANIMATIONS = {
  DURATION: {
    SHORT: 200,
    MEDIUM: 300,
    LONG: 500,
  },
  EASING: {
    EASE_IN_OUT: 'ease-in-out',
    EASE_OUT: 'ease-out',
  },
};

// Orientation handling
export const getOrientationStyles = (isLandscape) => ({
  container: {
    flexDirection: isLandscape && isTablet ? 'row' : 'column',
  },
  headerContainer: {
    paddingHorizontal: isLandscape ? wp('8%') : wp('6%'),
    paddingVertical: isLandscape ? hp('2%') : hp('3%'),
  },
  contentContainer: {
    flex: isLandscape && isTablet ? 0.7 : 1,
  },
  sidebarContainer: {
    flex: isLandscape && isTablet ? 0.3 : 0,
    display: isLandscape && isTablet ? 'flex' : 'none',
  },
});

// Export responsive utility functions
export const wp = responsiveWidth;
export const hp = responsiveHeight;

// Export all utilities as named exports for better tree shaking
export {
  getFontSize,
  getSpacing,
  normalize,
  isTablet,
  isSmallPhone,
  isLargePhone,
  LAYOUT,
  COLORS,
  TYPOGRAPHY,
  ANIMATIONS,
  getOrientationStyles,
};
